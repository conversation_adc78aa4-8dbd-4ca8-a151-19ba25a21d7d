using System.Collections.Generic;
using com.luxza.grandartslogic.domain.game;

namespace com.luxza.grandarts.domains.game.result.shanghai
{
    public struct ShangHaiAnalysisData
    {
        public readonly int SBullCount;
        public readonly int DBullCount;
        public readonly int T20Count;
        public readonly Dictionary<Award, int> AwardCounts;
        
        public ShangHaiAnalysisData(Dictionary<Award, int> awardCounts)
        {
            
        }
    }
}