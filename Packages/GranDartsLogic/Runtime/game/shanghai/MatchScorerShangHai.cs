using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.extentions;

namespace com.luxza.grandartslogic.domain.game.shanghai
{
    public class MatchScorerShangHai : IMatchScorer
    {
        private readonly List<PlayerShanghaiData> _playerdatas = new List<PlayerShanghaiData>();
        private readonly GameRuleShangHai _ruleShangHai;
        public MatchShangHai Match { get; }
        private NotSupportedException _notSupportedInputTypeException =
            new NotSupportedException("Round must be either PerDartsInput or PerVisitInput");
        
        public MatchScorerShangHai(MatchShangHai match)
        {
            Match = match;
            foreach (var t in match.ParticipantTeams.AllUnits)
            {
                foreach (var m in t.AllMember)
                {
                    _playerdatas.Add(new PlayerShanghaiData(t.Id, m.GranId));
                }
            }
        }
        
        public int CurrentScore(string unitId)
        {
            var unit = Match.ParticipantTeams.Unit(unitId);
            return unit.Progress.CurrentTotalScore(Score);
        }

        public int Score(string teamId, int roundIndex, int throwIndex)
        {
            var roundInput = Match.ParticipantTeams.Unit(teamId).Progress.Rounds[roundIndex].GetRoundComponent<IRoundInput>();
            if (roundInput is VisitInput perVisitInput)
            {
                return perVisitInput.Score;
            }

            if (roundInput is SegmentInput perDartsInput)
            {
                return Score(perDartsInput.Throws[throwIndex].VirtualHitArea);
            }

            throw _notSupportedInputTypeException;
        }

        public int TotalScoreInRound(string teamId, int roundIndex)
        {
            var roundInput = Match.ParticipantTeams.Unit(teamId).Progress.Rounds[roundIndex].GetRoundComponent<IRoundInput>();
            if (roundInput is SegmentInput
                perDartsInput)
            {
                return perDartsInput.Throws.Sum(t => Score(t.VirtualHitArea));
            }

            if (roundInput is VisitInput perVisitInput)
            {
                return perVisitInput.Score;
            }

            throw _notSupportedInputTypeException;
        }
        
        /// <summary>
        /// Roundのスコアを返します。
        /// </summary>
        /// <param name="round"></param>
        /// <returns></returns>
        public int TotalScoreInRound(Round round)
        {
            if (round.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                return segmentInput.Throws.Sum(t => Score(t.VirtualHitArea));
            }
            else if (round.TryGetRoundComponent<VisitInput>(out var visitInput))
            {
                return visitInput.Score;
            }

            throw new InvalidRoundInput("This round has no SegmentInput or VisitInput");
        }

        public int Score(Segment hit)
        {
            if (hit == null)
            {
                return 0;
            }
            if (hit.IsSingle)
            {
                return 1;
            }
            if (hit.IsDouble)
            {
                return 2;
            }
            if (hit.IsTriple)
            {
                return 3;
            }
            return 0;
        }

        public int GetTotalScore(string unitId)
        {
            return _playerdatas
                .Where(p => p.TeamId == unitId)
                .Sum(p => p.TotalScore);
        }

        public int GetRoundScore(string unitId, int roundIndex)
        {
            return _playerdatas
                .Where(p => p.TeamId == unitId)
                .Sum(p => p.GetRoundScore(roundIndex));
        }

        public bool IsShanghaiAchieved(string unitId)
        {
            return _playerdatas
                .Where(p => p.TeamId == unitId)
                .Any(p => p.IsShanghaiAchieved());
        }

        public void RecordHit(string teamId, string granId, int segmentValue, int roundIndex)
        {
            var playerData = _playerdatas.First(p => p.TeamId == teamId && p.GranId == granId);
            playerData.RecordHit(segmentValue, roundIndex);
        }

        /// <summary>
        /// 回退指定玩家的最后一镖
        /// </summary>
        /// <param name="teamId">队伍ID</param>
        /// <param name="granId">玩家GranID</param>
        /// <returns>是否成功回退</returns>
        public bool RevertLastThrow(string teamId, string granId)
        {
            var playerData = _playerdatas.First(p => p.TeamId == teamId && p.GranId == granId);
            return playerData.RevertLastThrow();
        }

        /// <summary>
        /// 回退指定玩家指定轮次的最后一镖
        /// </summary>
        /// <param name="teamId">队伍ID</param>
        /// <param name="granId">玩家GranID</param>
        /// <param name="roundIndex">轮次索引（0-based）</param>
        /// <returns>是否成功回退</returns>
        public bool RevertThrowInRound(string teamId, string granId, int roundIndex)
        {
            var playerData = _playerdatas.First(p => p.TeamId == teamId && p.GranId == granId);
            return playerData.RevertThrowInRound(roundIndex);
        }

        /// <summary>
        /// 回退指定玩家的整个轮次
        /// </summary>
        /// <param name="teamId">队伍ID</param>
        /// <param name="granId">玩家GranID</param>
        /// <param name="roundIndex">轮次索引（0-based）</param>
        /// <returns>是否成功回退</returns>
        public bool RevertRound(string teamId, string granId, int roundIndex)
        {
            var playerData = _playerdatas.First(p => p.TeamId == teamId && p.GranId == granId);
            return playerData.RevertRound(roundIndex);
        }

        /// <summary>
        /// 回退指定单位的最后一镖（适用于单人队伍）
        /// </summary>
        /// <param name="unitId">单位ID</param>
        /// <returns>是否成功回退</returns>
        public bool RevertLastThrowForUnit(string unitId)
        {
            var playerData = _playerdatas.FirstOrDefault(p => p.TeamId == unitId);
            if (playerData == null) return false;
            return playerData.RevertLastThrow();
        }

        /// <summary>
        /// 回退指定单位的整个轮次（适用于单人队伍）
        /// </summary>
        /// <param name="unitId">单位ID</param>
        /// <param name="roundIndex">轮次索引（0-based）</param>
        /// <returns>是否成功回退</returns>
        public bool RevertRoundForUnit(string unitId, int roundIndex)
        {
            var playerData = _playerdatas.FirstOrDefault(p => p.TeamId == unitId);
            if (playerData == null) return false;
            return playerData.RevertRound(roundIndex);
        }

        private class PlayerShanghaiData
        {
            public string TeamId { get; }
            public string GranId { get; }
            private readonly List<int> _scores = new List<int>();
            private readonly Dictionary<int, List<int>> _roundHits = new Dictionary<int, List<int>>();

            public PlayerShanghaiData(string teamId, string granId)
            {
                TeamId = teamId;
                GranId = granId;
            }

            public void RecordHit(int segmentValue, int roundIndex)
            {
                if (!_roundHits.ContainsKey(roundIndex))
                {
                    _roundHits[roundIndex] = new List<int>();
                }

                _roundHits[roundIndex].Add(segmentValue);
                
                // Calculate score for this throw
                int score = 0;
                if (segmentValue == 1) score = 1;
                else if (segmentValue == 2) score = 2;
                else if (segmentValue == 3) score = 3;
                
                _scores.Add(score);
            }

            public int GetRoundScore(int roundIndex)
            {
                return _roundHits.ContainsKey(roundIndex) ? 
                    _roundHits[roundIndex].Sum() : 0;
            }

            public int TotalScore => _scores.Sum();

            /// <summary>
            /// 回退最后一镖
            /// </summary>
            /// <returns>是否成功回退</returns>
            public bool RevertLastThrow()
            {
                if (_scores.Count == 0) return false;

                // 移除最后一个分数
                _scores.RemoveAt(_scores.Count - 1);

                // 从最后一个轮次中移除最后一镖
                var lastRoundWithHits = _roundHits.Where(kvp => kvp.Value.Count > 0)
                                                  .OrderByDescending(kvp => kvp.Key)
                                                  .FirstOrDefault();

                if (lastRoundWithHits.Value != null && lastRoundWithHits.Value.Count > 0)
                {
                    lastRoundWithHits.Value.RemoveAt(lastRoundWithHits.Value.Count - 1);

                    // 如果轮次没有命中记录了，移除该轮次
                    if (lastRoundWithHits.Value.Count == 0)
                    {
                        _roundHits.Remove(lastRoundWithHits.Key);
                    }

                    return true;
                }

                return false;
            }

            /// <summary>
            /// 回退指定轮次的最后一镖
            /// </summary>
            /// <param name="roundIndex">轮次索引（0-based）</param>
            /// <returns>是否成功回退</returns>
            public bool RevertThrowInRound(int roundIndex)
            {
                if (!_roundHits.ContainsKey(roundIndex) || _roundHits[roundIndex].Count == 0)
                {
                    return false;
                }

                // 移除该轮次的最后一镖
                var roundHits = _roundHits[roundIndex];
                var lastHit = roundHits[roundHits.Count - 1];
                roundHits.RemoveAt(roundHits.Count - 1);

                // 从总分中移除对应的分数
                int scoreToRemove = 0;
                if (lastHit == 1) scoreToRemove = 1;
                else if (lastHit == 2) scoreToRemove = 2;
                else if (lastHit == 3) scoreToRemove = 3;

                // 从_scores列表中移除最后一个匹配的分数
                for (int i = _scores.Count - 1; i >= 0; i--)
                {
                    if (_scores[i] == scoreToRemove)
                    {
                        _scores.RemoveAt(i);
                        break;
                    }
                }

                // 如果轮次没有命中记录了，移除该轮次
                if (roundHits.Count == 0)
                {
                    _roundHits.Remove(roundIndex);
                }

                return true;
            }

            /// <summary>
            /// 回退整个轮次
            /// </summary>
            /// <param name="roundIndex">轮次索引（0-based）</param>
            /// <returns>是否成功回退</returns>
            public bool RevertRound(int roundIndex)
            {
                if (!_roundHits.ContainsKey(roundIndex))
                {
                    return false;
                }

                var roundHits = _roundHits[roundIndex];

                // 计算该轮次的总分数并从_scores中移除
                foreach (var hit in roundHits)
                {
                    int scoreToRemove = 0;
                    if (hit == 1) scoreToRemove = 1;
                    else if (hit == 2) scoreToRemove = 2;
                    else if (hit == 3) scoreToRemove = 3;

                    // 从_scores列表中移除最后一个匹配的分数
                    for (int i = _scores.Count - 1; i >= 0; i--)
                    {
                        if (_scores[i] == scoreToRemove)
                        {
                            _scores.RemoveAt(i);
                            break;
                        }
                    }
                }

                // 移除整个轮次
                _roundHits.Remove(roundIndex);

                return true;
            }

            public bool IsShanghaiAchieved()
            {
                // Shanghai规则：在同一轮中命中该轮对应数字的Single(1)、Double(2)和Triple(3)
                return _roundHits.Values.Any(roundHits =>
                    roundHits.Contains(1) && roundHits.Contains(2) && roundHits.Contains(3));
            }
        }
    }
}
