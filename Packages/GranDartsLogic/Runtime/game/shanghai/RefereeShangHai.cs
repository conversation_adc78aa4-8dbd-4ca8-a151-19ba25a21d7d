using System.Collections.Generic;
using System.Linq;
using com.luxza.granlog;

namespace com.luxza.grandartslogic.domain.game.shanghai
{
    public class RefereeShangHai : BaseReferee<MatchShangHai, MatchScorerShangHai, GameEventPublisher>
    {
        internal RefereeShangHai(MatchShangHai match) 
            : base(match, new MatchScorer<PERSON>hang<PERSON><PERSON>(match))
        {
        }
        
        public override bool IsReachGameEnd
        {
            get
            {
                // Check if any player achieved Shanghai
                if (Match.ParticipantTeams.AllUnits.Any(unit => Scorer.IsShanghaiAchieved(unit.Id)))
                {
                    return true;
                }
                
                // Check if we've reached the end round
                return Match.IsReachEndRound;
            }
        }

        public override bool IsNextRoundOverMaxRound { get; }

        public override void AcceptHit(Segment segment, System.Numerics.Vector2? hitPosition)
        {
            if (IsReachGameEnd)
            {
                Log.d("Receive key. but already finished!");
                return;
            }

            var currentRoundNo = CurrentRoundAtCurrentTeam.No;

            // 检查是否打中了当前轮次对应的目标
            bool isHit = IsValidHitForCurrentRound(segment, currentRoundNo);

            // 确定虚拟命中区域：如果命中目标则为实际区域，否则为Miss
            Segment virtualHit = isHit ? segment : Segment.Miss;

            // 记录投掷（无论是否命中都要记录）
            Match.StoreHit(_currentThrowingUnitIndex, segment, virtualHit, hitPosition);
            
            if (isHit)
            {
                // Determine the segment value (1 for single, 2 for double, 3 for triple)
                int segmentValue = 0;
                if (segment.IsSingle) segmentValue = 1;
                else if (segment.IsDouble) segmentValue = 2;
                else if (segment.IsTriple) segmentValue = 3;

                // Record the hit for Shanghai scoring
                Scorer.RecordHit(CurrentUnit.Id, CurrentUnit.CurrentThrower.GranId, segmentValue, currentRoundNo - 1);

                // Check if Shanghai was achieved
                if (Scorer.IsShanghaiAchieved(CurrentUnit.Id))
                {
                    GameEnd();
                    return;
                }
            }
            else
            {
                // 没有打中目标，记录为Miss
                Log.d($"Round {currentRoundNo}: Hit {segment.Code} but expected target for round {currentRoundNo}");
            }
            EventPublisher?.PublishUpdateProgress(CurrentUnit);
            // Move to next turn
            if (Match.IsReachEndOfRound(_currentThrowingUnitIndex))
            {
                EventPublisher.PublishEndTurn(CurrentUnit);
            }

            // Check if game should end after this round
            if (Match.IsReachEndRound)
            {
                GameEnd();
            }
        }

        /// <summary>
        /// 检查当前投掷是否命中了当前轮次的有效目标
        /// </summary>
        /// <param name="segment">投掷命中的区域</param>
        /// <param name="roundNo">当前轮次号</param>
        /// <returns>是否为有效命中</returns>
        private bool IsValidHitForCurrentRound(Segment segment, int roundNo)
        {
            if (roundNo < 1 || roundNo > 20)
            {
                return false;
            }

            // 检查是否命中了当前轮次对应的数字
            // 例如：第1轮只能命中1的区域（S1_In, D1, T1）
            // 第2轮只能命中2的区域（S2_In, D2, T2），以此类推

            if (segment.PositionCode == roundNo)
            {
                // 检查是否为有效的命中类型（Single, Double, Triple）
                return segment.IsSingle || segment.IsDouble || segment.IsTriple;
            }

            return false;
        }
        
        public override void ChangeToNextTeam()
        {
            EndRoundAtCurrentTeam();
            FixRoundAtCurrentUnit();
            if (IsNextRoundOverMaxRound)
            {
                GameEnd();
            }
            else
            {
                var sender = CurrentUnit;
                _currentThrowingUnitIndex = NextTeamIndex(_currentThrowingUnitIndex);
                StartRoundAtCurrentTeam();
                EventPublisher.PublishChange(sender);
            }
        }
        
        protected virtual int NextTeamIndex(int currentTeamIndex)
        {
            return (currentTeamIndex + 1 >= Match.ParticipantTeams.Count) ? 0 : currentTeamIndex + 1;
        }

        public override int TotalScoreAtCurrentThrowingTeam { get; }

        public override int CurrentScore(string unitId)
        {
            return Scorer.GetTotalScore(unitId);
        }

        public override void RefreshGameData()
        {
            
        }

        protected override void GameEnd()
        {
            MatchFinishStatus matchFinishStatus = CalculateRanking();
            EventPublisher.PublishFinishMatch(matchFinishStatus);
        }

        public override IEnumerable<(Round round, int score)> RoundsAndScoresAt(string unitId)
        {
            return AllRoundsAt(unitId).Select(r => (r, Scorer.TotalScoreInRound(r)));
        }

        public override Award[] AchievableAward => GameRuleShangHai.AchievableAwards;

        /// <summary>
        /// 回退当前玩家的最后一镖
        /// </summary>
        /// <returns>是否成功回退</returns>
        private bool RevertLastThrow()
        {
            var currentPlayer = CurrentUnit.CurrentThrower;
            bool success = Scorer.RevertLastThrow(CurrentUnit.Id, currentPlayer.GranId);

            if (success)
            {
                EventPublisher?.PublishUpdateProgress(CurrentUnit);
                Log.d($"Reverted last throw for player {currentPlayer.GranId}");
            }

            return success;
        }

        /// <summary>
        /// 回退当前玩家的整个轮次
        /// </summary>
        /// <param name="roundIndex">轮次索引（0-based）</param>
        /// <returns>是否成功回退</returns>
        private bool RevertRound(int roundIndex)
        {
            var currentPlayer = CurrentUnit.CurrentThrower;
            bool success = Scorer.RevertRound(CurrentUnit.Id, currentPlayer.GranId, roundIndex);

            if (success)
            {
                EventPublisher?.PublishUpdateProgress(CurrentUnit);
                Log.d($"Reverted round {roundIndex + 1} for player {currentPlayer.GranId}");
            }

            return success;
        }

        protected override void ResetRoundData()
        {
            RevertRound(CurrentRoundAtCurrentTeam.No);
        }

        protected override void ResetThrowData()
        {
            RevertLastThrow();
        }
    }
}
